package com.unimas.asn.http;

import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.ConfigXmlOperator;
import com.unimas.asn.util.Constant;
import com.unimas.asn.util.DeviceConfigReader;

/**
 * 请求验证器类，用于验证不同类型的请求
 */
public class RequestValidator {
    
    /**
     * 验证请求帧的基本结构
     * @param frame 请求帧
     * @throws ValidationException 如果验证失败
     */
    public static void validateRequestFrame(MessageRequestFrame frame) throws ValidationException {
        if (frame == null||frame.getContent() == null||frame.getVersion() == null) {
            throw new ValidationException("请求帧不能为空", ProcessErrorState.messageStructureError,null);
        }
    }
    
    /**
     * 验证服务配置请求
     * @param request 服务配置请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceConfigRequest(ServiceConfigRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务配置请求不能为空", ProcessErrorState.messageStructureError,null);
        }
        
        // 根据ServiceConfigRequest的CHOICE结构验证
        try {
            if (request.hasAddServiceRequest()) {
                validateAddServiceRequest(request.getAddServiceRequest());
            } else if (request.hasUpdateServiceRequest()) {
                validateUpdateServiceRequest(request.getUpdateServiceRequest());
            } else if (request.hasDeleteServiceRequest()) {
                validateDeleteServiceRequest(request.getDeleteServiceRequest());
            } else {
                throw new ValidationException("无效的服务配置请求类型", ProcessErrorState.messageStructureError,null);
            }
        } catch (Exception e) {
            if (e instanceof ValidationException) {
                throw (ValidationException) e;
            }
            throw new ValidationException("服务配置请求验证异常: " + e.getMessage(), ProcessErrorState.messageStructureError,null);
        }
    }
    
    /**
     * 验证添加服务请求
     */
    private static void validateAddServiceRequest(AddServiceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("添加服务请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.addService);
        }

        
        if (request.getDisplayname() == null || new String(request.getDisplayname().byteArrayValue()).isEmpty()) {
            throw new ValidationException("显示名称不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
        }

        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
        }
        if(!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())){
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
        }
    }
    
    /**
     * 验证更新服务请求
     */
    private static void validateUpdateServiceRequest(UpdateServiceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("更新服务请求不能为空", ProcessErrorState.messageStructureError,ContentMessageType.updateService);
        }
        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.updateService);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.updateService);
        }
        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.updateService);
        }
        if(!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())){
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError,ContentMessageType.updateService);
        }
    }
    
    /**
     * 验证删除服务请求
     */
    private static void validateDeleteServiceRequest(DeleteServiceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("删除服务请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.deleteService);
        }

        
        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.deleteService);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.deleteService);
        }
    }
    
    /**
     * 验证服务控制请求
     * @param request 服务控制请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceControlRequest(ServiceControlRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务控制请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.controlService);
        }

        try {
            if (request.getServiceId() == null) {
                throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.controlService);
            }
            if(!isServiceExist(request.getServiceId().intValue()+"")){
                throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.controlService);
            }
            if (request.getServiceStartOrStop() == null) {
                throw new ValidationException("服务启停状态不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.controlService);
            }
        } catch (Exception e) {
            if(e instanceof ValidationException){
                throw e;
            }else{
                throw new ValidationException("服务控制请求验证异常: " + e.getMessage(),ProcessErrorState.illegalArgumentError,ContentMessageType.controlService);
            }
        }
    }
    
    /**
     * 验证服务状态查询请求
     * @param request 服务状态查询请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceStatusQueryRequest(ServiceStatusQueryRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务状态查询请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.queryServiceStatus);
        }
        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceStatus);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.queryServiceStatus);
        }

    }
    
    /**
     * 验证服务配置查询请求
     * @param request 服务配置查询请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceConfigQueryRequest(ServiceConfigQueryRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务配置查询请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.queryServiceConfig);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceConfig);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.queryServiceConfig);
        }
        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceConfig);
        }
        if(!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())){
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceConfig);
        }

    }
    
    /**
     * 验证报警报告请求
     * @param request 报警报告请求
     * @throws ValidationException 如果验证失败
     */
//    public static void validateAlarmReportRequest(AlarmReportRequest request) throws ValidationException {
//        if (request == null) {
//            throw new ValidationException("报警报告请求不能为空", 400);
//        }
//
//        try {
//            if (request.getServiceId() == null) {
//                throw new ValidationException("服务ID不能为空", 400);
//            }
//
//            if (request.getAlarmType() == null) {
//                throw new ValidationException("报警类型不能为空", 400);
//            }
//
//            if (request.getAlarmCode() == null) {
//                throw new ValidationException("报警代码不能为空", 400);
//            }
//
//            if (request.getAlarmStatus() == null) {
//                throw new ValidationException("报警状态不能为空", 400);
//            }
//        } catch (Exception e) {
//            throw new ValidationException("报警报告请求验证异常: " + e.getMessage(), 400);
//        }
//    }
    
    /**
     * 验证工作状态请求
     * @param request 工作状态请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateWorkStatusRequest(WorkStatusRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("工作状态请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.queryWorkStatus);
        }


        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryWorkStatus);
        }

    }

    /**
     * 验证发送包统计请求
     * @param request 发送包统计请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateSendPacketStatsRequest(SendPacketStatsRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("发送包统计请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.sendPacketStats);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        // 验证网络配置是否与当前设备配置匹配
        if (!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())) {
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        // 如果有period参数，验证其有效性
        if (request.hasPeriod()) {
            if (request.getPeriod().intValue() <= 0) {
                throw new ValidationException("时间段参数必须大于0", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
            }
        }
    }
    
    /**
     * 检查是否包含特殊字符（可用于防止注入攻击）
     * @param input 输入字符串
     * @return 是否包含特殊字符
     */
    public static boolean containsSpecialChars(String input) {
        if (input == null) {
            return false;
        }
        return input.matches(".*[;\\\\'\\\"/\\[\\]<>].*");
    }
    private static boolean isServiceExist(String serviceId) {
        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        return configXmlOperator.isServiceExist(serviceId);
    }
} 