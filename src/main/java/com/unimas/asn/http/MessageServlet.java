package com.unimas.asn.http;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.unimas.asn.codec.OssServAdapter;
import com.unimas.asn.logic.ServiceConfigLogic;
import com.unimas.asn.logic.ServiceConfigQueryLogic;
import com.unimas.asn.logic.ServiceStatusLogic;
import com.unimas.asn.db.UdpAuditDAO;

import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.*;
import com.unimassystem.main.License;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



/**
 * 处理ASN.1消息的HTTP Servlet
 */
public class MessageServlet extends HttpServlet {
    private static final Logger logger = LoggerFactory.getLogger(MessageServlet.class);

    // 授权模块名称
    private static final String SG_BASE_MODEL = "baseModel";

    /**
     * 检查授权是否有效
     * @return 授权是否有效
     */
    private boolean isLicenseValid() {
        boolean ret = DeviceConfigReader.getInstance().getNetwork().equals(Network.sender);
        if(ret ){ //发送验证
            try {
                ret = new License().isValidTime(SG_BASE_MODEL);
            } catch (Exception e) {
                logger.error("检查授权时发生错误", e);
                ret =  false;
            }
        }else{ //接收不验证
            ret = true;
        }
        return ret;
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 检查授权是否有效
        if (!isLicenseValid()) {
            logger.error("授权已过期，拒绝处理请求");
            sendHttpErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, "授权已过期，请联系管理员");
            return;
        }
        try {
            // 读取请求体
            byte[] requestBody = readRequestBody(request);
            logger.info("request body is {}", Hex.toHexString(requestBody));
            // 解码请求
            MessageRequestFrame frame = OssServAdapter.decode(requestBody, MessageRequestFrame.class);
            logger.info("decode message frame is {}", frame);
            // 处理消息
            byte[] responseData = processMessage(frame);
            logger.info("response data is {}", Hex.toHexString(responseData));
            // 发送响应
            response.setContentType("application/octet-stream");
            response.setContentLength(responseData.length);
            try (OutputStream os = response.getOutputStream()) {
                os.write(responseData);
                os.flush();
            }
        } catch (Exception e) {
            logger.error("Error processing request", e);
//            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            sendHttpErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器内部错误");
        }
    }

    private byte[] readRequestBody(HttpServletRequest request) throws IOException {
        int contentLength = request.getContentLength();
        if (contentLength <= 0) {
            throw new IOException("Invalid content length: " + contentLength);
        }

        byte[] buffer = new byte[contentLength];
        try (InputStream is = request.getInputStream()) {
            int totalBytesRead = 0;
            while (totalBytesRead < contentLength) {
                int bytesRead = is.read(buffer, totalBytesRead, contentLength - totalBytesRead);
                if (bytesRead == -1) {
                    break;
                }
                totalBytesRead += bytesRead;
            }
            if (totalBytesRead != contentLength) {
                throw new IOException("Expected " + contentLength + " bytes but read " + totalBytesRead + " bytes");
            }
            return buffer;
        }
    }

    private byte[] processMessage(MessageRequestFrame frame) throws IOException {
        // 再次检查授权是否有效，防止doPost方法被绕过
        if (!isLicenseValid()) {
            logger.error("授权已过期，拒绝处理消息");
            throw new IOException("授权已过期");
        }

        try {
            // 验证请求帧
            RequestValidator.validateRequestFrame(frame);

            MessageRequestFrame.Content content = frame.getContent();
            // 创建响应帧
            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(frame.getVersion());
            responseFrame.setContent(new MessageResponseFrame.Content());

            if (content.hasServiceConfigRequest()) {
                try {
                    ServiceConfigRequest request = content.getServiceConfigRequest();
                    // 验证服务配置请求
                    RequestValidator.validateServiceConfigRequest(request);
                    ServiceConfigResponse serviceConfigResponse = processServiceConfigRequest(request);
                    responseFrame.getContent().setServiceConfigResponse(serviceConfigResponse);
                } catch (ValidationException e) {
                    // 创建验证错误响应
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务配置请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceControlRequest()) {
                try {
                    ServiceControlRequest request = content.getServiceControlRequest();
                    // 验证服务控制请求
                    RequestValidator.validateServiceControlRequest(request);
                    ServiceControlResponse serviceControlResponse = processServiceControlRequest(request);
                    responseFrame.getContent().setServiceControlResponse(serviceControlResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务控制请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceStatusQueryRequest()) {
                try {
                    ServiceStatusQueryRequest request = content.getServiceStatusQueryRequest();
                    // 验证服务状态查询请求
                    RequestValidator.validateServiceStatusQueryRequest(request);
                    ServiceStatusQueryResponse serviceStatusQueryResponse = processServiceStatusQueryRequest(request);
                    responseFrame.getContent().setServiceStatusQueryResponse(serviceStatusQueryResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务状态查询请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceConfigQueryRequest()) {
                try {
                    ServiceConfigQueryRequest request = content.getServiceConfigQueryRequest();
                    // 验证服务配置查询请求
                    RequestValidator.validateServiceConfigQueryRequest(request);
                    ServiceConfigQueryResponse serviceConfigQueryResponse = processServiceConfigQueryRequest(request);
                    responseFrame.getContent().setServiceConfigQueryResponse(serviceConfigQueryResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务配置查询请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasAlarmReportRequest()) {
                AlarmReportRequest request = content.getAlarmReportRequest();
                // 验证报警报告请求
//                    RequestValidator.validateAlarmReportRequest(request);
                AlarmReportResponse alarmReportResponse = processAlarmReportRequest(request);
                responseFrame.getContent().setAlarmReportResponse(alarmReportResponse);
            } else if (content.hasWorkStatusRequest()) {
                try {
                    WorkStatusRequest request = content.getWorkStatusRequest();
                    // 验证工作状态请求
                    RequestValidator.validateWorkStatusRequest(request);
                    WorkStatusResponse workStatusResponse = processWorkStatusRequest(request);
                    responseFrame.getContent().setWorkStatusResponse(workStatusResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("工作状态请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasGetAllServiceIdsRequest()){
                GetAllServiceIdsRequest request = content.getGetAllServiceIdsRequest();
                GetAllServiceIdsResponse getAllServiceIdsResponse = processGetAllServiceIdsRequest(request);
                responseFrame.getContent().setGetAllServiceIdsResponse(getAllServiceIdsResponse);
            } else if(content.hasSendPacketStatsRequest()){
                try {
                    SendPacketStatsRequest request = content.getSendPacketStatsRequest();
                    // 验证发送包统计请求
                    RequestValidator.validateSendPacketStatsRequest(request);
                    SendPacketStatsResponse sendPacketStatsResponse = processSendPacketStatsRequest(request);
                    responseFrame.getContent().setSendPacketStatsResponse(sendPacketStatsResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("发送包统计请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasReceivePacketStatsRequest()){

            } else if(content.hasCheckCommStatusRequest()){

                CheckCommStatusRequest checkCommStatusRequest = content.getCheckCommStatusRequest();
                CheckCommStatusResponse checkCommStatusResponse = processCheckCommStatusRequest(checkCommStatusRequest);
                responseFrame.getContent().setCheckCommStatusResponse(checkCommStatusResponse);
            }else if(content.hasSetMngCfgRequest()){

            }else {
                // 未知的请求类型
                ErrorResponse errorResponse = createErrorResponse( null, ProcessErrorState.messageStructureError);
                responseFrame.getContent().setError(errorResponse);
                logger.error("收到未知的请求类型");
            }

            logger.info("response frame is {}", responseFrame);
            // 编码响应
            return OssServAdapter.encode(responseFrame);
        } catch (ValidationException e) {
            // 处理顶层验证异常
            MessageResponseFrame errorFrame = createErrorResponseFrame(frame.getVersion().intValue(), e);
            logger.error("请求帧验证失败: {}", e.getMessage());
            return OssServAdapter.encode(errorFrame);
        }
    }

    /**
     * 创建带有错误信息的响应帧
     */
    private MessageResponseFrame createErrorResponseFrame(int version, ValidationException e) {
        MessageResponseFrame responseFrame = new MessageResponseFrame();
        responseFrame.setVersion(new Uint8(version));
        responseFrame.setContent(new MessageResponseFrame.Content());
        ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
        responseFrame.getContent().setError(errorResponse);
        return responseFrame;
    }

    /**
     * 创建错误响应对象
     */
    private ErrorResponse createErrorResponse(ContentMessageType messageType, ProcessErrorState code) {
        ErrorResponse error = new ErrorResponse();
        error.setErrorState(code);
        error.setMessageType(messageType);
        return error;
    }

    private ServiceConfigResponse processServiceConfigRequest(ServiceConfigRequest request) throws ValidationException {
        logger.info("Processing ServiceConfigRequest");
        ServiceConfigLogic serviceConfigLogic = new ServiceConfigLogic();
        return serviceConfigLogic.serviceConfigRequest(request);
    }

    private ServiceControlResponse processServiceControlRequest(ServiceControlRequest request) throws ValidationException {
        logger.info("Processing ServiceControlRequest for service: {}, action: {}",
            request.getServiceId(), request.getServiceStartOrStop());
        ServiceStatusLogic serviceStatusLogic = new ServiceStatusLogic();
        return serviceStatusLogic.startOrStop(request);
    }

    private ServiceStatusQueryResponse processServiceStatusQueryRequest(ServiceStatusQueryRequest request) {
        logger.info("Processing ServiceStatusQueryRequest for service: {}", request.getServiceId());
        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        int runFlag = 0;
        try {
            runFlag = configXmlOperator.getIsRun(request.getServiceId().intValue()+"")?0:1;
        } catch (Exception e) {
            logger.error("Error processing ServiceStatusQueryRequest", e);
            runFlag = 2;
        }
        ServiceStatusQueryResponse response = new ServiceStatusQueryResponse();
        response.setMessageType(ContentMessageType.queryServiceStatus);
        response.setServiceId(request.getServiceId());
        response.setServiceStatus(ServiceStatus.valueOf(runFlag)); // 示例状态
        return response;
    }

    private ServiceConfigQueryResponse processServiceConfigQueryRequest(ServiceConfigQueryRequest request) {
        logger.info("Processing ServiceConfigQueryRequest for service: {}, network: {}",
            request.getServiceId(), request.getNetwork());
        return new ServiceConfigQueryLogic().serviceConfigQuery(request);
    }

    private AlarmReportResponse processAlarmReportRequest(AlarmReportRequest request) {
        logger.info("Processing AlarmReportRequest for service: {}, type: {}",
            request.getServiceId(), request.getAlarmType());
        logger.info("alarm info = " + request);
        AlarmReportResponse response = new AlarmReportResponse();
        response.setMessageType(ContentMessageType.reportAlarm);
        response.setServiceId(request.getServiceId());
        return response;
    }

    private GetAllServiceIdsResponse processGetAllServiceIdsRequest(GetAllServiceIdsRequest request) {
        logger.info("Processing GetAllServiceIdsRequest");

        // 创建响应对象
        GetAllServiceIdsResponse response = new GetAllServiceIdsResponse();
        response.setMessageType(ContentMessageType.getAllServiceIds);

        // 创建服务ID集合
        GetAllServiceIdsResponse.ServiceIds serviceIds = new GetAllServiceIdsResponse.ServiceIds();

        try {
            // 使用ConfigXmlOperator获取所有服务ID
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);

            // 使用新增的getAllServiceIds方法获取所有服务ID
            List<Integer> allServiceIds = configXmlOperator.getAllServiceIds();
            // 将服务ID添加到响应中
            for (Integer id : allServiceIds) {
                serviceIds.add(new ServiceId(id));
            }
            // 设置服务ID列表到响应
            response.setServiceIds(serviceIds);
        } catch (Exception e) {
            logger.error("Error processing GetAllServiceIdsRequest", e);
            // 发生错误时返回空列表
            response.setServiceIds(serviceIds);
        }

        return response;
    }

    /**
     * 处理检查通信状态请求
     * 使用NetworkLinkStatusReader工具类获取网卡状态
     */
    private CheckCommStatusResponse processCheckCommStatusRequest(CheckCommStatusRequest request) {
        logger.info("Processing CheckCommStatusRequest for service: {}, network: {}",
            request.getServiceId(), request.getNetwork());

        // 创建响应对象
        CheckCommStatusResponse response = new CheckCommStatusResponse();
        response.setMessageType(request.getMessageType());
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        try {
            // 使用NetworkLinkStatusReader获取网卡状态
            NetworkLinkStatusReader.LinkStatus linkStatus = NetworkLinkStatusReader.getLastLinkStatus();

            if (linkStatus != null) {
                // 设置连接状态
                boolean isConnected = "UP".equals(linkStatus.getStatus());
                response.setIsConnected(isConnected);

                // 设置连接事件时间
                LocalDateTime timestamp = linkStatus.getTimestamp();
                if (timestamp != null) {
                    // 将LocalDateTime转换为毫秒时间戳
                    long epochMilli = timestamp.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(epochMilli))));
                } else {
                    // 如果时间戳为空，使用当前时间
                    response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
                }

                logger.info("Network link status: {}, timestamp: {}", linkStatus.getStatus(), timestamp);
            } else {
                // 如果无法获取网卡状态，默认为断开状态
                response.setIsConnected(false);
                response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
                logger.warn("Unable to get network link status, defaulting to disconnected");
            }

        } catch (Exception e) {
            // 发生异常时，默认为断开状态
            response.setIsConnected(false);
            response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
            logger.error("Error processing CheckCommStatusRequest", e);
        }

        return response;
    }

    private WorkStatusResponse processWorkStatusRequest(WorkStatusRequest request) {
        logger.info("Processing WorkStatusRequest for service: {}", request.getServiceId());
        WorkStatusResponse response = new WorkStatusResponse();
        response.setMessageType(ContentMessageType.queryWorkStatus);
        response.setServiceId(request.getServiceId());

        try {
            // 获取设备角色（发送端或接收端）
            DeviceConfigReader deviceConfig = DeviceConfigReader.getInstance();
            Network role = deviceConfig.getNetwork();

            // 从/proc/net/dev获取网络统计数据
            Map<String, Long> stats = NetworkStatsReader.getNetworkStats(role);

            // 设置实际数据
            response.setSendPkgNumToday(new Uint32(stats.get("sendPkgNum").intValue()));
            response.setSendPkgSizeToday(new Uint32(stats.get("sendPkgSize").intValue()));
            response.setRecvPkgNumToday(new Uint32(stats.get("recvPkgNum").intValue()));
            response.setRecvPkgSizeToday(new Uint32(stats.get("recvPkgSize").intValue()));

            logger.info("Network stats for {} role: send packets={}, send size={}, recv packets={}, recv size={}",
                    role, stats.get("sendPkgNum"), stats.get("sendPkgSize"),
                    stats.get("recvPkgNum"), stats.get("recvPkgSize"));
        } catch (Exception e) {
            logger.error("Error getting network stats, using default values", e);
            // 出错时使用默认值
            response.setSendPkgNumToday(new Uint32(0));
            response.setSendPkgSizeToday(new Uint32(0));
            response.setRecvPkgNumToday(new Uint32(0));
            response.setRecvPkgSizeToday(new Uint32(0));
        }

        // 设置设备时间
        Uint64 l = new Uint64();
        l.setValue(new BigInteger(String.valueOf(System.currentTimeMillis())));
        response.setDevTime(l);
        return response;
    }

    /**
     * 处理发送包统计请求
     * @param request 发送包统计请求
     * @return 发送包统计响应
     */
    private SendPacketStatsResponse processSendPacketStatsRequest(SendPacketStatsRequest request) {
        logger.info("Processing SendPacketStatsRequest for service: {}, network: {}",
                request.getServiceId(), request.getNetwork());

        // 创建响应对象
        SendPacketStatsResponse response = new SendPacketStatsResponse();
        response.setMessageType(ContentMessageType.sendPacketStats);
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        // 如果请求中有period，则设置到响应中
        if (request.hasPeriod()) {
            response.setPeriod(request.getPeriod());
        }

        try {
            // 创建UdpAuditDAO实例
            UdpAuditDAO udpAuditDAO = new UdpAuditDAO();

            // 获取包统计数据
            List<com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats> packetStatsList;
            String serviceId = request.getServiceId().toString();

            if (request.hasPeriod()) {
                // 如果有时间段参数，按时间段查询
                int periodDays = request.getPeriod().intValue();
                packetStatsList = udpAuditDAO.getPacketStatsByServiceIdAndPeriod(serviceId, periodDays);
                logger.info("Fetched packet stats for service {} with period {} days: {} records",
                        serviceId, periodDays, packetStatsList.size());
            } else {
                // 否则查询所有数据
                packetStatsList = udpAuditDAO.getPacketStatsByServiceId(serviceId);
                logger.info("Fetched packet stats for service {}: {} records",
                        serviceId, packetStatsList.size());
            }

            // 创建PacketStats集合并设置数据
            SendPacketStatsResponse.PacketStats responsePacketStats = new SendPacketStatsResponse.PacketStats();

            // 将查询结果添加到响应中
            for (com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats stats : packetStatsList) {
                responsePacketStats.add(stats);
            }

            response.setPacketStats(responsePacketStats);

            logger.info("Successfully processed SendPacketStatsRequest for service: {}", serviceId);

        } catch (Exception e) {
            logger.error("Error processing SendPacketStatsRequest for service: {}",
                    request.getServiceId(), e);

            // 发生错误时返回空的统计数据
            SendPacketStatsResponse.PacketStats emptyPacketStats = new SendPacketStatsResponse.PacketStats();
            response.setPacketStats(emptyPacketStats);
        }

        return response;
    }

    private void sendHttpErrorResponse(HttpServletResponse response, int errorCode, String errorMessage) {
        try {
            // 如果是401未授权错误，直接返回纯文本响应
            if (errorCode == HttpServletResponse.SC_UNAUTHORIZED) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("授权已过期，请联系管理员");
                return;
            }

            // 其他错误返回ASN.1格式的错误响应
            response.setContentType("application/octet-stream");

            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(new Uint8(1));
            responseFrame.setContent(new MessageResponseFrame.Content());

            // 创建错误响应对象
            ErrorResponse errorResponse = new ErrorResponse();
            errorResponse.setErrorState(ProcessErrorState.messageStructureError);
            errorResponse.setMessageType(ContentMessageType.queryServiceStatus);
            // 设置错误响应
            responseFrame.getContent().setError(errorResponse);

            byte[] responseData = OssServAdapter.encode(responseFrame);
            response.setContentLength(responseData.length);
            try (OutputStream os = response.getOutputStream()) {
                os.write(responseData);
                os.flush();
            }

        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
        }
    }
}