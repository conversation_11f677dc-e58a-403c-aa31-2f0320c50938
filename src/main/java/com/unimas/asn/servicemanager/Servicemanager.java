/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
  A class for the Servicemanager ASN.1/Java  project.
*/
public class Servicemanager extends ASN1Project {

    /**
     * Initialize the pdu decoder.
     */
    private static final ProjectInfo c_projectInfo = new ProjectInfo (
	null,
	new byte[] {
	    (byte)0x0b, (byte)0xff, (byte)0xc4, (byte)0x9f, (byte)0xdd,
	    (byte)0x9e, (byte)0x4f, (byte)0x4a, (byte)0x46, (byte)0x9e,
	    (byte)0x25, (byte)0x01, (byte)0x2e, (byte)0x05, (byte)0xa7,
	    (byte)0x6b, (byte)0x53, (byte)0xfc, (byte)0x82, (byte)0xb6,
	    (byte)0x7e, (byte)0x54, (byte)0xbf, (byte)0x4b, (byte)0x16,
	    (byte)0xfe, (byte)0xd2, (byte)0x15, (byte)0xbd, (byte)0x9d,
	    (byte)0x74, (byte)0x34, (byte)0x4f, (byte)0x7a, (byte)0x85,
	    (byte)0xea, (byte)0xa5, (byte)0x9b, (byte)0x29, (byte)0x47,
	    (byte)0x66, (byte)0x1b, (byte)0x4f, (byte)0x9d, (byte)0x3d,
	    (byte)0x70, (byte)0x0f, (byte)0xbd, (byte)0x7f, (byte)0x99,
	    (byte)0x96, (byte)0x16, (byte)0x4b, (byte)0xbf, (byte)0x54,
	    (byte)0x76, (byte)0xd7, (byte)0x27, (byte)0x29, (byte)0xf6,
	    (byte)0xe0, (byte)0x02, (byte)0x9e, (byte)0x89, (byte)0xbc,
	    (byte)0xc4, (byte)0xaa, (byte)0x03, (byte)0x04, (byte)0xcf,
	    (byte)0x5b
	},
	"2025/06/12"
    );
    
    /**
     * Get the project descriptor of 'this' object.
     */
    public ProjectInfo getProjectInfo()
    {
	return c_projectInfo;
    }
    
    private static final ASN1Project c_project = new Servicemanager();

    /**
     * Methods for accessing Coders.
     */
    public static Coder getDefaultCoder()
    {
	return createCOERCoder(c_project);
    }
    
    public static OERCoder getOERCoder()
    {
	return createOERCoder(c_project);
    }
    
    public static COERCoder getCOERCoder()
    {
	return createCOERCoder(c_project);
    }
    
}
