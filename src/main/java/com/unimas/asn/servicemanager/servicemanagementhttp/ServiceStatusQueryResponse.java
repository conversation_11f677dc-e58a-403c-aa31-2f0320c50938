/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ServiceStatusQueryResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class ServiceStatusQueryResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public ServiceStatusQueryResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public ServiceStatusQueryResponse(ContentMessageType messageType, 
		    ServiceId serviceId, ServiceStatus serviceStatus)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setServiceStatus(serviceStatus);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.addService;
	mComponents[1] = new ServiceId();
	mComponents[2] = ServiceStatus.running;
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[3];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.addService;
	    case 1:
		return new ServiceId();
	    case 2:
		return ServiceStatus.running;
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "serviceId"
    public ServiceId getServiceId()
    {
	return (ServiceId)mComponents[1];
    }
    
    public void setServiceId(ServiceId serviceId)
    {
	mComponents[1] = serviceId;
    }
    
    
    // Methods for field "serviceStatus"
    public ServiceStatus getServiceStatus()
    {
	return (ServiceStatus)mComponents[2];
    }
    
    public void setServiceStatus(ServiceStatus serviceStatus)
    {
	mComponents[2] = serviceStatus;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ServiceStatusQueryResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ServiceStatusQueryResponse"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"addService",
					0
				    ),
				    new MemberListElement (
					"updateService",
					1
				    ),
				    new MemberListElement (
					"deleteService",
					2
				    ),
				    new MemberListElement (
					"controlService",
					3
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					4
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					5
				    ),
				    new MemberListElement (
					"reportAlarm",
					6
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					7
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					8
				    ),
				    new MemberListElement (
					"sendPacketStats",
					9
				    ),
				    new MemberListElement (
					"receivePacketStats",
					10
				    ),
				    new MemberListElement (
					"checkCommStatusRequest",
					11
				    ),
				    new MemberListElement (
					"setMngCfgRequest",
					12
				    )
				}
			    ),
			    0,
			    ContentMessageType.addService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ServiceId"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ServiceId"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new ServiceId(0), 
				    new ServiceId(32767),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(32767)
			    ),
			    null,
			    2
			)
		    ),
		    "serviceId",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ServiceStatus"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ServiceStatus"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"running",
					0
				    ),
				    new MemberListElement (
					"stopped",
					1
				    )
				}
			    ),
			    0,
			    ServiceStatus.running
			)
		    ),
		    "serviceStatus",
		    2,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ServiceStatusQueryResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ServiceStatusQueryResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for ServiceStatusQueryResponse
