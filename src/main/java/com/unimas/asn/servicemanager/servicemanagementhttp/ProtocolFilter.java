/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ProtocolFilter ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see BitString
 */

public class ProtocolFilter extends BitString {
    
    /**
     * The default constructor.
     */
    public ProtocolFilter()
    {
    }
    
    /**
     * Construct a BIT STRING from a byte array.
     * All bits considered significant.
     * @param value the byte array to set this object to.
     */
    public ProtocolFilter(byte[] value)
    {
	super(value);
    }
    
    
    /**
     * Construct a BIT STRING from a byte array and significant bits.
     * @param value the byte array to set this object to.
     * @param sigBits the number of significant bits.
     */
    public ProtocolFilter(byte[] value, int sigBits)
    {
	super(value, sigBits);
    }
    
    // Named list definitions.
    
    public static final int crcfilter = 0;
    public static final int asnfilter = 1;
    
    /**
     * Initialize the type descriptor.
     */
    private static final BitStringInfo c_typeinfo = new BitStringInfo (
	new Tags (
	    new short[] {
		0x0003
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ProtocolFilter"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ProtocolFilter"
	),
	536607,
	new SizeConstraint (
	    new ExtensibleConstraint (
		new SingleValueConstraint (
		    new com.oss.asn1.INTEGER(8)
		),
		null
	    )
	),
	new Bounds (
	    java.lang.Long.valueOf(8),
	    java.lang.Long.valueOf(8)
	),
	new MemberList (
	    new MemberListElement[] {
		new MemberListElement (
		    "crcfilter",
		    0
		),
		new MemberListElement (
		    "asnfilter",
		    1
		)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ProtocolFilter object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ProtocolFilter object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for ProtocolFilter
