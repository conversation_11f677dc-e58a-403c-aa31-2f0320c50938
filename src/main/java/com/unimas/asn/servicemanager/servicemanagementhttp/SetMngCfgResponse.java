/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the SetMngCfgResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class SetMngCfgResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public SetMngCfgResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public SetMngCfgResponse(ContentMessageType messageType, 
		    IPAddress managementIP, PortNumber managementPort, 
		    IPAddress centerIP, PortNumber centerPort, 
		    BOOLEAN receiveStatus, UTF8String16 statusDescription)
    {
	setMessageType(messageType);
	setManagementIP(managementIP);
	setManagementPort(managementPort);
	setCenterIP(centerIP);
	setCenterPort(centerPort);
	setReceiveStatus(receiveStatus);
	setStatusDescription(statusDescription);
    }
    
    /**
     * Construct with components.
     */
    public SetMngCfgResponse(ContentMessageType messageType, 
		    IPAddress managementIP, PortNumber managementPort, 
		    IPAddress centerIP, PortNumber centerPort, 
		    boolean receiveStatus, UTF8String16 statusDescription)
    {
	this(messageType, managementIP, managementPort, centerIP, 
	     centerPort, new BOOLEAN(receiveStatus), statusDescription);
    }
    
    /**
     * Construct with required components.
     */
    public SetMngCfgResponse(ContentMessageType messageType, 
		    IPAddress managementIP, PortNumber managementPort, 
		    IPAddress centerIP, PortNumber centerPort, 
		    boolean receiveStatus)
    {
	setMessageType(messageType);
	setManagementIP(managementIP);
	setManagementPort(managementPort);
	setCenterIP(centerIP);
	setCenterPort(centerPort);
	setReceiveStatus(receiveStatus);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.addService;
	mComponents[1] = new IPAddress();
	mComponents[2] = new PortNumber();
	mComponents[3] = new IPAddress();
	mComponents[4] = new PortNumber();
	mComponents[5] = new BOOLEAN();
	mComponents[6] = new UTF8String16();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[7];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.addService;
	    case 1:
		return new IPAddress();
	    case 2:
		return new PortNumber();
	    case 3:
		return new IPAddress();
	    case 4:
		return new PortNumber();
	    case 5:
		return new BOOLEAN();
	    case 6:
		return new UTF8String16();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "managementIP"
    public IPAddress getManagementIP()
    {
	return (IPAddress)mComponents[1];
    }
    
    public void setManagementIP(IPAddress managementIP)
    {
	mComponents[1] = managementIP;
    }
    
    
    // Methods for field "managementPort"
    public PortNumber getManagementPort()
    {
	return (PortNumber)mComponents[2];
    }
    
    public void setManagementPort(PortNumber managementPort)
    {
	mComponents[2] = managementPort;
    }
    
    
    // Methods for field "centerIP"
    public IPAddress getCenterIP()
    {
	return (IPAddress)mComponents[3];
    }
    
    public void setCenterIP(IPAddress centerIP)
    {
	mComponents[3] = centerIP;
    }
    
    
    // Methods for field "centerPort"
    public PortNumber getCenterPort()
    {
	return (PortNumber)mComponents[4];
    }
    
    public void setCenterPort(PortNumber centerPort)
    {
	mComponents[4] = centerPort;
    }
    
    
    // Methods for field "receiveStatus"
    public boolean getReceiveStatus()
    {
	return ((BOOLEAN)mComponents[5]).booleanValue();
    }
    
    public void setReceiveStatus(boolean receiveStatus)
    {
	setReceiveStatus(new BOOLEAN(receiveStatus));
    }
    
    public void setReceiveStatus(BOOLEAN receiveStatus)
    {
	mComponents[5] = receiveStatus;
    }
    
    
    // Methods for field "statusDescription"
    public UTF8String16 getStatusDescription()
    {
	return (UTF8String16)mComponents[6];
    }
    
    public void setStatusDescription(UTF8String16 statusDescription)
    {
	mComponents[6] = statusDescription;
    }
    
    public boolean hasStatusDescription()
    {
	return componentIsPresent(6);
    }
    
    public void deleteStatusDescription()
    {
	setComponentAbsent(6);
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "SetMngCfgResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "SetMngCfgResponse"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"addService",
					0
				    ),
				    new MemberListElement (
					"updateService",
					1
				    ),
				    new MemberListElement (
					"deleteService",
					2
				    ),
				    new MemberListElement (
					"controlService",
					3
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					4
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					5
				    ),
				    new MemberListElement (
					"reportAlarm",
					6
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					7
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					8
				    ),
				    new MemberListElement (
					"sendPacketStats",
					9
				    ),
				    new MemberListElement (
					"receivePacketStats",
					10
				    ),
				    new MemberListElement (
					"checkCommStatusRequest",
					11
				    ),
				    new MemberListElement (
					"setMngCfgRequest",
					12
				    )
				}
			    ),
			    0,
			    ContentMessageType.addService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "managementIP",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "managementPort",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "centerIP",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "centerPort",
		    4,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new TypeInfo (
			    new Tags (
				new short[] {
				    (short)0x8005
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"BOOLEAN"
			    ),
			    new QName (
				"builtin",
				"BOOLEAN"
			    ),
			    536603,
			    null
			)
		    ),
		    "receiveStatus",
		    5,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8006
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"UTF8String16"
			    ),
			    new QName (
				"builtin",
				"UTF8String16"
			    ),
			    536603,
			    null,
			    null
			)
		    ),
		    "statusDescription",
		    6,
		    3,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8005, 5)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8006, 6)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' SetMngCfgResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' SetMngCfgResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for SetMngCfgResponse
