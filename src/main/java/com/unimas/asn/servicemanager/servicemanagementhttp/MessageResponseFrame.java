/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the MessageResponseFrame ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class MessageResponseFrame extends Sequence {
    
    /**
     * The default constructor.
     */
    public MessageResponseFrame()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public MessageResponseFrame(Uint8 version, Content content)
    {
	setVersion(version);
	setContent(content);
    }
    
    public void initComponents()
    {
	mComponents[0] = new Uint8();
	mComponents[1] = new Content();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new Uint8();
	    case 1:
		return new Content();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "version"
    public Uint8 getVersion()
    {
	return (Uint8)mComponents[0];
    }
    
    public void setVersion(Uint8 version)
    {
	mComponents[0] = version;
    }
    
    
    // Methods for field "content"
    public Content getContent()
    {
	return (Content)mComponents[1];
    }
    
    public void setContent(Content content)
    {
	mComponents[1] = content;
    }
    
    
    
    /**
     * Define the Content ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
     * @see Choice
     */
    public static class Content extends Choice {
	
	/**
	 * The default constructor.
	 */
	public Content()
	{
	}
	
	public static final  int  serviceConfigResponse_chosen = 1;
	public static final  int  serviceControlResponse_chosen = 2;
	public static final  int  serviceStatusQueryResponse_chosen = 3;
	public static final  int  serviceConfigQueryResponse_chosen = 4;
	public static final  int  alarmReportResponse_chosen = 5;
	public static final  int  workStatusResponse_chosen = 6;
	public static final  int  getAllServiceIdsResponse_chosen = 7;
	public static final  int  sendPacketStatsResponse_chosen = 8;
	public static final  int  receivePacketStatsResponse_chosen = 9;
	public static final  int  checkCommStatusResponse_chosen = 10;
	public static final  int  setMngCfgResponse_chosen = 11;
	public static final  int  error_chosen = 12;
	
	// Methods for field "serviceConfigResponse"
	public static Content createContentWithServiceConfigResponse(ServiceConfigResponse serviceConfigResponse)
	{
	    Content __object = new Content();

	    __object.setServiceConfigResponse(serviceConfigResponse);
	    return __object;
	}
	
	public boolean hasServiceConfigResponse()
	{
	    return getChosenFlag() == serviceConfigResponse_chosen;
	}
	
	public ServiceConfigResponse getServiceConfigResponse()
	{
	    if (hasServiceConfigResponse())
		return (ServiceConfigResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceConfigResponse(ServiceConfigResponse serviceConfigResponse)
	{
	    setChosenValue(serviceConfigResponse);
	    setChosenFlag(serviceConfigResponse_chosen);
	}
	
	
	// Methods for field "serviceControlResponse"
	public static Content createContentWithServiceControlResponse(ServiceControlResponse serviceControlResponse)
	{
	    Content __object = new Content();

	    __object.setServiceControlResponse(serviceControlResponse);
	    return __object;
	}
	
	public boolean hasServiceControlResponse()
	{
	    return getChosenFlag() == serviceControlResponse_chosen;
	}
	
	public ServiceControlResponse getServiceControlResponse()
	{
	    if (hasServiceControlResponse())
		return (ServiceControlResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceControlResponse(ServiceControlResponse serviceControlResponse)
	{
	    setChosenValue(serviceControlResponse);
	    setChosenFlag(serviceControlResponse_chosen);
	}
	
	
	// Methods for field "serviceStatusQueryResponse"
	public static Content createContentWithServiceStatusQueryResponse(ServiceStatusQueryResponse serviceStatusQueryResponse)
	{
	    Content __object = new Content();

	    __object.setServiceStatusQueryResponse(serviceStatusQueryResponse);
	    return __object;
	}
	
	public boolean hasServiceStatusQueryResponse()
	{
	    return getChosenFlag() == serviceStatusQueryResponse_chosen;
	}
	
	public ServiceStatusQueryResponse getServiceStatusQueryResponse()
	{
	    if (hasServiceStatusQueryResponse())
		return (ServiceStatusQueryResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceStatusQueryResponse(ServiceStatusQueryResponse serviceStatusQueryResponse)
	{
	    setChosenValue(serviceStatusQueryResponse);
	    setChosenFlag(serviceStatusQueryResponse_chosen);
	}
	
	
	// Methods for field "serviceConfigQueryResponse"
	public static Content createContentWithServiceConfigQueryResponse(ServiceConfigQueryResponse serviceConfigQueryResponse)
	{
	    Content __object = new Content();

	    __object.setServiceConfigQueryResponse(serviceConfigQueryResponse);
	    return __object;
	}
	
	public boolean hasServiceConfigQueryResponse()
	{
	    return getChosenFlag() == serviceConfigQueryResponse_chosen;
	}
	
	public ServiceConfigQueryResponse getServiceConfigQueryResponse()
	{
	    if (hasServiceConfigQueryResponse())
		return (ServiceConfigQueryResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setServiceConfigQueryResponse(ServiceConfigQueryResponse serviceConfigQueryResponse)
	{
	    setChosenValue(serviceConfigQueryResponse);
	    setChosenFlag(serviceConfigQueryResponse_chosen);
	}
	
	
	// Methods for field "alarmReportResponse"
	public static Content createContentWithAlarmReportResponse(AlarmReportResponse alarmReportResponse)
	{
	    Content __object = new Content();

	    __object.setAlarmReportResponse(alarmReportResponse);
	    return __object;
	}
	
	public boolean hasAlarmReportResponse()
	{
	    return getChosenFlag() == alarmReportResponse_chosen;
	}
	
	public AlarmReportResponse getAlarmReportResponse()
	{
	    if (hasAlarmReportResponse())
		return (AlarmReportResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setAlarmReportResponse(AlarmReportResponse alarmReportResponse)
	{
	    setChosenValue(alarmReportResponse);
	    setChosenFlag(alarmReportResponse_chosen);
	}
	
	
	// Methods for field "workStatusResponse"
	public static Content createContentWithWorkStatusResponse(WorkStatusResponse workStatusResponse)
	{
	    Content __object = new Content();

	    __object.setWorkStatusResponse(workStatusResponse);
	    return __object;
	}
	
	public boolean hasWorkStatusResponse()
	{
	    return getChosenFlag() == workStatusResponse_chosen;
	}
	
	public WorkStatusResponse getWorkStatusResponse()
	{
	    if (hasWorkStatusResponse())
		return (WorkStatusResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setWorkStatusResponse(WorkStatusResponse workStatusResponse)
	{
	    setChosenValue(workStatusResponse);
	    setChosenFlag(workStatusResponse_chosen);
	}
	
	
	// Methods for field "getAllServiceIdsResponse"
	public static Content createContentWithGetAllServiceIdsResponse(GetAllServiceIdsResponse getAllServiceIdsResponse)
	{
	    Content __object = new Content();

	    __object.setGetAllServiceIdsResponse(getAllServiceIdsResponse);
	    return __object;
	}
	
	public boolean hasGetAllServiceIdsResponse()
	{
	    return getChosenFlag() == getAllServiceIdsResponse_chosen;
	}
	
	public GetAllServiceIdsResponse getGetAllServiceIdsResponse()
	{
	    if (hasGetAllServiceIdsResponse())
		return (GetAllServiceIdsResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setGetAllServiceIdsResponse(GetAllServiceIdsResponse getAllServiceIdsResponse)
	{
	    setChosenValue(getAllServiceIdsResponse);
	    setChosenFlag(getAllServiceIdsResponse_chosen);
	}
	
	
	// Methods for field "sendPacketStatsResponse"
	public static Content createContentWithSendPacketStatsResponse(SendPacketStatsResponse sendPacketStatsResponse)
	{
	    Content __object = new Content();

	    __object.setSendPacketStatsResponse(sendPacketStatsResponse);
	    return __object;
	}
	
	public boolean hasSendPacketStatsResponse()
	{
	    return getChosenFlag() == sendPacketStatsResponse_chosen;
	}
	
	public SendPacketStatsResponse getSendPacketStatsResponse()
	{
	    if (hasSendPacketStatsResponse())
		return (SendPacketStatsResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setSendPacketStatsResponse(SendPacketStatsResponse sendPacketStatsResponse)
	{
	    setChosenValue(sendPacketStatsResponse);
	    setChosenFlag(sendPacketStatsResponse_chosen);
	}
	
	
	// Methods for field "receivePacketStatsResponse"
	public static Content createContentWithReceivePacketStatsResponse(ReceivePacketStatsResponse receivePacketStatsResponse)
	{
	    Content __object = new Content();

	    __object.setReceivePacketStatsResponse(receivePacketStatsResponse);
	    return __object;
	}
	
	public boolean hasReceivePacketStatsResponse()
	{
	    return getChosenFlag() == receivePacketStatsResponse_chosen;
	}
	
	public ReceivePacketStatsResponse getReceivePacketStatsResponse()
	{
	    if (hasReceivePacketStatsResponse())
		return (ReceivePacketStatsResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setReceivePacketStatsResponse(ReceivePacketStatsResponse receivePacketStatsResponse)
	{
	    setChosenValue(receivePacketStatsResponse);
	    setChosenFlag(receivePacketStatsResponse_chosen);
	}
	
	
	// Methods for field "checkCommStatusResponse"
	public static Content createContentWithCheckCommStatusResponse(CheckCommStatusResponse checkCommStatusResponse)
	{
	    Content __object = new Content();

	    __object.setCheckCommStatusResponse(checkCommStatusResponse);
	    return __object;
	}
	
	public boolean hasCheckCommStatusResponse()
	{
	    return getChosenFlag() == checkCommStatusResponse_chosen;
	}
	
	public CheckCommStatusResponse getCheckCommStatusResponse()
	{
	    if (hasCheckCommStatusResponse())
		return (CheckCommStatusResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setCheckCommStatusResponse(CheckCommStatusResponse checkCommStatusResponse)
	{
	    setChosenValue(checkCommStatusResponse);
	    setChosenFlag(checkCommStatusResponse_chosen);
	}
	
	
	// Methods for field "setMngCfgResponse"
	public static Content createContentWithSetMngCfgResponse(SetMngCfgResponse setMngCfgResponse)
	{
	    Content __object = new Content();

	    __object.setSetMngCfgResponse(setMngCfgResponse);
	    return __object;
	}
	
	public boolean hasSetMngCfgResponse()
	{
	    return getChosenFlag() == setMngCfgResponse_chosen;
	}
	
	public SetMngCfgResponse getSetMngCfgResponse()
	{
	    if (hasSetMngCfgResponse())
		return (SetMngCfgResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setSetMngCfgResponse(SetMngCfgResponse setMngCfgResponse)
	{
	    setChosenValue(setMngCfgResponse);
	    setChosenFlag(setMngCfgResponse_chosen);
	}
	
	
	// Methods for field "error"
	public static Content createContentWithError(ErrorResponse error)
	{
	    Content __object = new Content();

	    __object.setError(error);
	    return __object;
	}
	
	public boolean hasError()
	{
	    return getChosenFlag() == error_chosen;
	}
	
	public ErrorResponse getError()
	{
	    if (hasError())
		return (ErrorResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setError(ErrorResponse error)
	{
	    setChosenValue(error);
	    setChosenFlag(error_chosen);
	}
	
	
	// Method to create a specific choice instance
	public AbstractData createInstance(int chosen)
	{
	    switch (chosen) {
		case serviceConfigResponse_chosen:
		    return new ServiceConfigResponse();
		case serviceControlResponse_chosen:
		    return new ServiceControlResponse();
		case serviceStatusQueryResponse_chosen:
		    return new ServiceStatusQueryResponse();
		case serviceConfigQueryResponse_chosen:
		    return new ServiceConfigQueryResponse();
		case alarmReportResponse_chosen:
		    return new AlarmReportResponse();
		case workStatusResponse_chosen:
		    return new WorkStatusResponse();
		case getAllServiceIdsResponse_chosen:
		    return new GetAllServiceIdsResponse();
		case sendPacketStatsResponse_chosen:
		    return new SendPacketStatsResponse();
		case receivePacketStatsResponse_chosen:
		    return new ReceivePacketStatsResponse();
		case checkCommStatusResponse_chosen:
		    return new CheckCommStatusResponse();
		case setMngCfgResponse_chosen:
		    return new SetMngCfgResponse();
		case error_chosen:
		    return new ErrorResponse();
		default:
		    throw new InternalError("Choice.createInstance()");
	    }
	    
	}
	
	/**
	 * Initialize the type descriptor.
	 */
	private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	    new Tags (
		new short[] {
		    (short)0x8001
		}
	    ),
	    new QName (
		"com.unimas.asn.servicemanager.servicemanagementhttp",
		"MessageResponseFrame$Content"
	    ),
	    new QName (
		"builtin",
		"CHOICE"
	    ),
	    536607,
	    null,
	    new FieldsList (
		new FieldInfo[] {
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8000
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceConfigResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceConfigResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceConfigResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceConfigResponse"
				    )
				),
				0
			    )
			),
			"serviceConfigResponse",
			0,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8001
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceControlResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceControlResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceControlResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceControlResponse"
				    )
				),
				0
			    )
			),
			"serviceControlResponse",
			1,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8002
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceStatusQueryResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceStatusQueryResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceStatusQueryResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceStatusQueryResponse"
				    )
				),
				0
			    )
			),
			"serviceStatusQueryResponse",
			2,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8003
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ServiceConfigQueryResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ServiceConfigQueryResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceConfigQueryResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ServiceConfigQueryResponse"
				    )
				),
				0
			    )
			),
			"serviceConfigQueryResponse",
			3,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8004
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "AlarmReportResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "AlarmReportResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"AlarmReportResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"AlarmReportResponse"
				    )
				),
				0
			    )
			),
			"alarmReportResponse",
			4,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8005
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "WorkStatusResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "WorkStatusResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"WorkStatusResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"WorkStatusResponse"
				    )
				),
				0
			    )
			),
			"workStatusResponse",
			5,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8006
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "GetAllServiceIdsResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "GetAllServiceIdsResponse"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"GetAllServiceIdsResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"GetAllServiceIdsResponse"
				    )
				),
				0
			    )
			),
			"getAllServiceIdsResponse",
			6,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8007
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "SendPacketStatsResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "SendPacketStatsResponse"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SendPacketStatsResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SendPacketStatsResponse"
				    )
				),
				0
			    )
			),
			"sendPacketStatsResponse",
			7,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8008
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ReceivePacketStatsResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ReceivePacketStatsResponse"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ReceivePacketStatsResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ReceivePacketStatsResponse"
				    )
				),
				0
			    )
			),
			"receivePacketStatsResponse",
			8,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8009
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "CheckCommStatusResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "CheckCommStatusResponse"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"CheckCommStatusResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"CheckCommStatusResponse"
				    )
				),
				0
			    )
			),
			"checkCommStatusResponse",
			9,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800a
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "SetMngCfgResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "SetMngCfgResponse"
				),
				536603,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetMngCfgResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"SetMngCfgResponse"
				    )
				),
				0
			    )
			),
			"setMngCfgResponse",
			10,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x800b
				    }
				),
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "ErrorResponse"
				),
				new QName (
				    "ServiceManagementHTTP",
				    "ErrorResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ErrorResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.unimas.asn.servicemanager.servicemanagementhttp",
					"ErrorResponse"
				    )
				),
				0
			    )
			),
			"error",
			11,
			2
		    )
		}
	    ),
	    0,
	    new TagDecoder (
		new TagDecoderElement[] {
		    new TagDecoderElement((short)0x8000, 0),
		    new TagDecoderElement((short)0x8001, 1),
		    new TagDecoderElement((short)0x8002, 2),
		    new TagDecoderElement((short)0x8003, 3),
		    new TagDecoderElement((short)0x8004, 4),
		    new TagDecoderElement((short)0x8005, 5),
		    new TagDecoderElement((short)0x8006, 6),
		    new TagDecoderElement((short)0x8007, 7),
		    new TagDecoderElement((short)0x8008, 8),
		    new TagDecoderElement((short)0x8009, 9),
		    new TagDecoderElement((short)0x800a, 10),
		    new TagDecoderElement((short)0x800b, 11)
		}
	    )
	);
	
	/**
	 * Get the type descriptor (TypeInfo) of 'this' Content object.
	 */
	public TypeInfo getTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Get the static type descriptor (TypeInfo) of 'this' Content object.
	 */
	public static TypeInfo getStaticTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Check the current selection on unknown extension
	 */
	public final boolean hasUnknownExtension()
	{
	    return getChosenFlag() > 12;
	}
	
    } // End class definition for Content

    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "MessageResponseFrame"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "MessageResponseFrame"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint8"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint8"
			    ),
			    536603,
			    new Intersection (
				new ValueRangeConstraint (
				    new AbstractBounds(
					new Uint8(0), 
					new Uint8(255),
					0
				    )
				),
				new SingleValueConstraint (
				    new Uint8(1)
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1),
				java.lang.Long.valueOf(1)
			    ),
			    null,
			    1
			)
		    ),
		    "version",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new QName (
			    "com.unimas.asn.servicemanager.servicemanagementhttp",
			    "MessageResponseFrame$Content"
			)
		    ),
		    "content",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' MessageResponseFrame object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' MessageResponseFrame object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for MessageResponseFrame
