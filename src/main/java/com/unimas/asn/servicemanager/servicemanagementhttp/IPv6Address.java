/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the IPv6Address ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see OctetString
 */

public class IPv6Address extends OctetString {
    
    /**
     * The default constructor.
     */
    public IPv6Address()
    {
    }
    
    /**
     * Construct from a byte[] type.
     * @param value the byte[] object to set this object to.
     */
    
    public IPv6Address(byte[] value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final VectorInfo c_typeinfo = new VectorInfo (
	new Tags (
	    new short[] {
		0x0004
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "IPv6Address"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "IPv6Address"
	),
	536603,
	new SizeConstraint (
	    new SingleValueConstraint (
		new com.oss.asn1.INTEGER(16)
	    )
	),
	new Bounds (
	    java.lang.Long.valueOf(16),
	    java.lang.Long.valueOf(16)
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' IPv6Address object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' IPv6Address object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for IPv6Address
