/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ServiceConfigRequest ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Choice
 */

public class ServiceConfigRequest extends Choice {
    
    /**
     * The default constructor.
     */
    public ServiceConfigRequest()
    {
    }
    
    public static final  int  addServiceRequest_chosen = 1;
    public static final  int  updateServiceRequest_chosen = 2;
    public static final  int  deleteServiceRequest_chosen = 3;
    
    // Methods for field "addServiceRequest"
    public static ServiceConfigRequest createServiceConfigRequestWithAddServiceRequest(AddServiceRequest addServiceRequest)
    {
	ServiceConfigRequest __object = new ServiceConfigRequest();

	__object.setAddServiceRequest(addServiceRequest);
	return __object;
    }
    
    public boolean hasAddServiceRequest()
    {
	return getChosenFlag() == addServiceRequest_chosen;
    }
    
    public AddServiceRequest getAddServiceRequest()
    {
	if (hasAddServiceRequest())
	    return (AddServiceRequest)mChosenValue;
	else
	    return null;
    }
    
    public void setAddServiceRequest(AddServiceRequest addServiceRequest)
    {
	setChosenValue(addServiceRequest);
	setChosenFlag(addServiceRequest_chosen);
    }
    
    
    // Methods for field "updateServiceRequest"
    public static ServiceConfigRequest createServiceConfigRequestWithUpdateServiceRequest(UpdateServiceRequest updateServiceRequest)
    {
	ServiceConfigRequest __object = new ServiceConfigRequest();

	__object.setUpdateServiceRequest(updateServiceRequest);
	return __object;
    }
    
    public boolean hasUpdateServiceRequest()
    {
	return getChosenFlag() == updateServiceRequest_chosen;
    }
    
    public UpdateServiceRequest getUpdateServiceRequest()
    {
	if (hasUpdateServiceRequest())
	    return (UpdateServiceRequest)mChosenValue;
	else
	    return null;
    }
    
    public void setUpdateServiceRequest(UpdateServiceRequest updateServiceRequest)
    {
	setChosenValue(updateServiceRequest);
	setChosenFlag(updateServiceRequest_chosen);
    }
    
    
    // Methods for field "deleteServiceRequest"
    public static ServiceConfigRequest createServiceConfigRequestWithDeleteServiceRequest(DeleteServiceRequest deleteServiceRequest)
    {
	ServiceConfigRequest __object = new ServiceConfigRequest();

	__object.setDeleteServiceRequest(deleteServiceRequest);
	return __object;
    }
    
    public boolean hasDeleteServiceRequest()
    {
	return getChosenFlag() == deleteServiceRequest_chosen;
    }
    
    public DeleteServiceRequest getDeleteServiceRequest()
    {
	if (hasDeleteServiceRequest())
	    return (DeleteServiceRequest)mChosenValue;
	else
	    return null;
    }
    
    public void setDeleteServiceRequest(DeleteServiceRequest deleteServiceRequest)
    {
	setChosenValue(deleteServiceRequest);
	setChosenFlag(deleteServiceRequest_chosen);
    }
    
    
    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case addServiceRequest_chosen:
		return new AddServiceRequest();
	    case updateServiceRequest_chosen:
		return new UpdateServiceRequest();
	    case deleteServiceRequest_chosen:
		return new DeleteServiceRequest();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ServiceConfigRequest"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ServiceConfigRequest"
	),
	536607,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"AddServiceRequest"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"AddServiceRequest"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "AddServiceRequest"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "AddServiceRequest"
				)
			    ),
			    0
			)
		    ),
		    "addServiceRequest",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"UpdateServiceRequest"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"UpdateServiceRequest"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "UpdateServiceRequest"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "UpdateServiceRequest"
				)
			    ),
			    0
			)
		    ),
		    "updateServiceRequest",
		    1,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"DeleteServiceRequest"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"DeleteServiceRequest"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "DeleteServiceRequest"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "DeleteServiceRequest"
				)
			    ),
			    0
			)
		    ),
		    "deleteServiceRequest",
		    2,
		    2
		)
	    }
	),
	0,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1),
		new TagDecoderElement((short)0x8002, 2)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ServiceConfigRequest object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ServiceConfigRequest object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Check the current selection on unknown extension
     */
    public final boolean hasUnknownExtension()
    {
	return getChosenFlag() > 3;
    }
    
} // End class definition for ServiceConfigRequest
