/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Fri May 30 09:37:42 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Int16 ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see INTEGER
 */

public class Int16 extends INTEGER {
    
    /**
     * The default constructor.
     */
    public Int16()
    {
    }
    
    public Int16(short value)
    {
	super(value);
    }
    
    public Int16(int value)
    {
	super(value);
    }
    
    public Int16(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "Int16"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Int16"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new Int16(-32768), 
		new Int16(32767),
		0
	    )
	),
	new Bounds (
	    java.lang.Long.valueOf(-32768),
	    java.lang.Long.valueOf(32767)
	),
	null,
	2
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' Int16 object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' Int16 object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for Int16
