package com.unimas.asn.db;

import com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats;
import com.unimas.asn.servicemanager.servicemanagementhttp.PacketType;
import com.oss.asn1.Uint32;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for udp_audit table
 */
public class UdpAuditDAO {
    private static final Logger logger = LoggerFactory.getLogger(UdpAuditDAO.class);

    private static final String SELECT_PACKET_STATS_BY_SERVICE =
            "SELECT pack_type, SUM(count) as total_count " +
            "FROM udp_audit " +
            "WHERE serviceid = ? " +
            "GROUP BY pack_type " +
            "ORDER BY pack_type";

    private static final String SELECT_PACKET_STATS_BY_SERVICE_AND_PERIOD =
            "SELECT pack_type, SUM(count) as total_count " +
            "FROM udp_audit " +
            "WHERE serviceid = ? AND begintime >= DATE_SUB(NOW(), INTERVAL ? DAY) " +
            "GROUP BY pack_type " +
            "ORDER BY pack_type";

    /**
     * 获取指定服务的包统计数据
     * @param serviceId 服务ID
     * @return 包统计数据列表
     */
    public List<PacketStats> getPacketStatsByServiceId(String serviceId) {
        List<PacketStats> packetStatsList = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_PACKET_STATS_BY_SERVICE)) {
            
            stmt.setString(1, serviceId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    PacketStats packetStats = mapResultSetToPacketStats(rs);
                    if (packetStats != null) {
                        packetStatsList.add(packetStats);
                    }
                }
            }
        } catch (SQLException e) {
            logger.error("Error fetching packet stats for service: {}", serviceId, e);
        }

        return packetStatsList;
    }

    /**
     * 获取指定服务在指定时间段内的包统计数据
     * @param serviceId 服务ID
     * @param periodDays 时间段（天数）
     * @return 包统计数据列表
     */
    public List<PacketStats> getPacketStatsByServiceIdAndPeriod(String serviceId, int periodDays) {
        List<PacketStats> packetStatsList = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_PACKET_STATS_BY_SERVICE_AND_PERIOD)) {
            
            stmt.setString(1, serviceId);
            stmt.setInt(2, periodDays);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    PacketStats packetStats = mapResultSetToPacketStats(rs);
                    if (packetStats != null) {
                        packetStatsList.add(packetStats);
                    }
                }
            }
        } catch (SQLException e) {
            logger.error("Error fetching packet stats for service: {} with period: {} days", serviceId, periodDays, e);
        }

        return packetStatsList;
    }

    /**
     * 将ResultSet映射为PacketStats对象
     * @param rs ResultSet对象
     * @return PacketStats对象
     * @throws SQLException SQL异常
     */
    private PacketStats mapResultSetToPacketStats(ResultSet rs) throws SQLException {
        int packType = rs.getInt("pack_type");
        long totalCount = rs.getLong("total_count");

        // 将数据库中的pack_type映射到PacketType枚举
        PacketType packetType = mapPackTypeToPacketType(packType);
        if (packetType == null) {
            logger.warn("Unknown pack_type: {}, skipping", packType);
            return null;
        }

        PacketStats packetStats = new PacketStats();
        packetStats.setPacketType(packetType);
        packetStats.setPacketCount(new Uint32((int) totalCount));

        return packetStats;
    }

    /**
     * 将数据库中的pack_type值映射到PacketType枚举
     * @param packType 数据库中的pack_type值
     * @return PacketType枚举值
     */
    private PacketType mapPackTypeToPacketType(int packType) {
        switch (packType) {
            case 1:
                return PacketType.cim;
            case 2:
                return PacketType.slgs;
            case 3:
                return PacketType.lcs;
            case 4:
                return PacketType.bsm;
            case 9:
                return PacketType.other;
            default:
                return null;
        }
    }
}
