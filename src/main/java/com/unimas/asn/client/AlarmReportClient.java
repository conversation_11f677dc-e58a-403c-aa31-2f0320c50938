package com.unimas.asn.client;


import com.unimas.asn.codec.OssCoerAdapter;
import com.unimas.asn.servicemanager.servicemanagementhttp.AlarmReportRequest;
import com.unimas.asn.servicemanager.servicemanagementhttp.MessageRequestFrame;
import com.unimas.asn.servicemanager.servicemanagementhttp.Uint8;
import com.unimas.asn.util.Constant;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;
import java.io.InputStream;

/**
 * Client for sending alarm reports to a specified IP and port
 */
public class AlarmReportClient {
    private static final Logger logger = LoggerFactory.getLogger(AlarmReportClient.class);
//    private static final ObjectMapper objectMapper = new ObjectMapper();
    private HttpClient client = new HttpClient("http://localhost:8080");
    private String destinationIp;
    private int destinationPort;
    private String path = "/alarm";
    private boolean enabled;
    private long lastModifiedTime = 0; // 记录配置文件的最后修改时间
    private File configFile;

    public AlarmReportClient() {
        configFile = new File(Constant.SERVICE_PRE_PATH+"application.properties");
        loadConfiguration();
        if (configFile.exists()) {
            lastModifiedTime = configFile.lastModified();
        }
    }


    private void loadConfiguration() {
        Properties properties = new Properties();
        String oldIp = destinationIp;
        int oldPort = destinationPort;
        boolean oldEnabled = enabled;

        if (!configFile.exists()) {
            logger.info("Configuration file not found, using default values");
            destinationIp = "127.0.0.1";
            destinationPort = 8080;
            enabled = false;
            return;
        }
        try (InputStream inputStream = new FileInputStream(configFile)) {
            properties.load(inputStream);

            enabled = Boolean.parseBoolean(properties.getProperty("alarm.report.enabled", "true"));
            destinationIp = properties.getProperty("alarm.report.destination.ip", "127.0.0.1");
            String sport =  properties.getProperty("alarm.report.destination.port", "8080");
            sport = sport.isEmpty()?"8080":sport;
            destinationPort = Integer.parseInt(sport);
            path = properties.getProperty("alarm.report.destination.path", "/alarm");
            // Only log if there are changes
            if (oldIp == null || !destinationIp.equals(oldIp) || destinationPort != oldPort || enabled != oldEnabled) {
                if (oldIp == null) { // First load
                    logger.info("Alarm report client configured to send to {}:{}, enabled={}",
                            destinationIp, destinationPort, enabled);
                } else {
                    logger.info("Alarm report configuration changed: {}:{} (enabled={}) -> {}:{} (enabled={})",
                            oldIp, oldPort, oldEnabled, destinationIp, destinationPort, enabled);
                }
            }

            // 创建HTTP客户端
            client = new HttpClient("http://"+destinationIp+":"+destinationPort);

        } catch (Exception e) {
            logger.info("Error reading configuration file, using default values", e);
            // Set default values
            destinationIp = "127.0.0.1";
            destinationPort = 8080;
            enabled = false;
        }
    }

    /**
     * 检查配置文件是否被修改，如果被修改则重新加载
     * @return 如果配置被重新加载返回true，否则返回false
     */
    public boolean checkAndReloadConfigIfNeeded() {
        // 如果文件不存在
        if (!configFile.exists()) {
            // 如果之前文件存在（lastModifiedTime > 0）但现在被删除了，重置为默认值
            if (lastModifiedTime > 0) {
                logger.warn("配置文件已被删除，恢复默认设置");
                destinationIp = "127.0.0.1";
                destinationPort = 8080;
                enabled = false;
                lastModifiedTime = 0;
                return true;
            }
            return false;
        }

        // 检查文件的最后修改时间
        long currentModifiedTime = configFile.lastModified();

        // 如果修改时间变了，说明文件被更新了
        if (currentModifiedTime != lastModifiedTime) {
            logger.info("检测到配置文件已更新，重新加载配置...");
            loadConfiguration();
            lastModifiedTime = currentModifiedTime;
            return true;
        }

        return false;
    }

    /**
     * Checks if alarm reporting is enabled
     * @return true if enabled, false otherwise
     */
    public boolean isEnabled() {
        checkAndReloadConfigIfNeeded();
        return enabled;
    }

    /**
     * Gets the current destination IP
     * @return destination IP address
     */
    public String getDestinationIp() {
        checkAndReloadConfigIfNeeded();
        return destinationIp;
    }

    /**
     * Gets the current destination port
     * @return destination port
     */
    public int getDestinationPort() {
        checkAndReloadConfigIfNeeded();
        return destinationPort;
    }

    /**
     * Sends an AlarmReportRequest to the configured destination
     * @param request AlarmReportRequest to send
     * @return true if send was successful, false otherwise
     */
    public boolean sendAlarmReport(AlarmReportRequest request) {
        // Check if alarm reporting is enabled
        if (!isEnabled()) {
            logger.info("Alarm reporting is disabled, skipping report for service: {}", request.getServiceId());
            return false;
        }

        try {
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setAlarmReportRequest(request);
            // 编码消息
            byte[] encodedData = null;
            encodedData = OssCoerAdapter.encode(frame);

            logger.info("Sending alarm report to {}:{} for service: {}",
                    getDestinationIp(), getDestinationPort(), request.getServiceId());
            logger.debug("Sending = {}", Hex.toHexString(encodedData));
            logger.debug(frame.toString());
            // 发送请求
            client.post(path, encodedData);
            return true;
        } catch (IOException e) {
            logger.error("IO error sending alarm report to {}:{}", getDestinationIp(), getDestinationPort(), e);
        }
        return false;
    }
}
